# ===============================================================================
# 癫痫单细胞转录组学研究主分析流水线
# 
# 作者: [您的姓名]
# 日期: 2024-08-02
# 版本: 1.0
# 
# 描述: 
# 本脚本是癫痫相关单细胞转录组学研究的主分析流水线，整合多组学数据，
# 识别关键代谢通路，构建预后预测模型。
# 
# 使用方法:
# Rscript 主分析流水线.R
# 
# 依赖:
# - R >= 4.3.0
# - 详见 install_packages.R
# ===============================================================================

# 清理环境
rm(list = ls())
gc()

# 设置工作目录
setwd("~/epilepsy_scrna")

# 加载必需的包
suppressPackageStartupMessages({
    library(Seurat)
    library(SeuratObject)
    library(dplyr)
    library(ggplot2)
    library(patchwork)
    library(viridis)
    library(harmony)
    library(SingleR)
    library(celldex)
    library(CellChat)
    library(monocle3)
    library(scMetabolism)
    library(WGCNA)
    library(hdWGCNA)
    library(clusterProfiler)
    library(org.Hs.eg.db)
    library(randomForest)
    library(xgboost)
    library(glmnet)
    library(pROC)
    library(plotly)
    library(pheatmap)
})

# 设置全局参数
set.seed(123)
options(future.globals.maxSize = 8000 * 1024^2)  # 8GB

# 创建输出目录
dir.create("results", showWarnings = FALSE, recursive = TRUE)
dir.create("figures", showWarnings = FALSE, recursive = TRUE)
dir.create("reports", showWarnings = FALSE, recursive = TRUE)

# 加载自定义函数
source("scripts/data_processing_functions.R")
source("scripts/analysis_functions.R")
source("scripts/visualization_functions.R")
source("scripts/modeling_functions.R")

# ===============================================================================
# 第一部分：数据加载与预处理
# ===============================================================================

cat("开始数据加载与预处理...\n")

# 1.1 加载公共数据集
cat("加载公共数据集...\n")

# 人类癫痫数据
human_datasets <- list()

# PRJNA1149456 - 难治性癫痫患者数据
prjna_data <- load_prjna_dataset("data/raw/PRJNA1149456")
human_datasets[["PRJNA1149456"]] <- prjna_data

# GSE201048 - 人类癫痫标本
gse201048_data <- load_geo_dataset("data/raw/GSE201048")
human_datasets[["GSE201048"]] <- gse201048_data

# GSE140393 - 人类癫痫相关数据
gse140393_data <- load_geo_dataset("data/raw/GSE140393")
human_datasets[["GSE140393"]] <- gse140393_data

# 1.2 加载小鼠数据
cat("加载小鼠时间序列数据...\n")

# GSE197015 - 人类和小鼠癫痫数据（提取小鼠部分）
mouse_datasets <- list()
gse197015_mouse <- load_mouse_data("data/raw/GSE197015")
mouse_datasets[["GSE197015_Day7"]] <- gse197015_mouse$day7
mouse_datasets[["User_Day30"]] <- load_user_data("data/raw/user_day30")

# 1.3 数据质量评估
cat("进行数据质量评估...\n")

# 人类数据质量评估
human_qc_results <- lapply(names(human_datasets), function(dataset_name) {
    assess_data_quality(human_datasets[[dataset_name]], dataset_name)
})
names(human_qc_results) <- names(human_datasets)

# 小鼠数据质量评估
mouse_qc_results <- lapply(names(mouse_datasets), function(dataset_name) {
    assess_data_quality(mouse_datasets[[dataset_name]], dataset_name)
})
names(mouse_qc_results) <- names(mouse_datasets)

# 生成质控报告
generate_qc_report(c(human_qc_results, mouse_qc_results), "results/quality_control_report.html")

# 1.4 数据预处理和整合
cat("进行数据预处理和整合...\n")

# 人类数据预处理
human_processed <- lapply(human_datasets, function(dataset) {
    preprocess_single_dataset(dataset)
})

# 人类数据整合
human_integrated <- integrate_human_datasets(human_processed)

# 小鼠数据预处理和整合
mouse_processed <- lapply(mouse_datasets, function(dataset) {
    preprocess_single_dataset(dataset, species = "mouse")
})

mouse_integrated <- integrate_mouse_timeseries(mouse_processed)

# 保存预处理结果
saveRDS(human_integrated, "data/processed/human_integrated.rds")
saveRDS(mouse_integrated, "data/processed/mouse_integrated.rds")

cat("数据预处理完成！\n")

# ===============================================================================
# 第二部分：细胞类型识别与注释
# ===============================================================================

cat("开始细胞类型识别与注释...\n")

# 2.1 自动化细胞类型注释
cat("进行自动化细胞类型注释...\n")

# 人类数据注释
human_annotated <- annotate_cell_types(human_integrated, species = "human")

# 小鼠数据注释
mouse_annotated <- annotate_cell_types(mouse_integrated, species = "mouse")

# 2.2 手动验证和精细注释
cat("进行手动验证和精细注释...\n")

# 验证注释质量
human_validated <- validate_and_refine_annotations(human_annotated)
mouse_validated <- validate_and_refine_annotations(mouse_annotated)

# 2.3 癫痫特异性细胞亚群识别
cat("识别癫痫特异性细胞亚群...\n")

# 差异表达分析
human_deg_results <- find_epilepsy_markers(human_validated)

# 癫痫特异性基因模块分析
human_wgcna <- perform_epilepsy_WGCNA(human_validated)
epilepsy_modules <- identify_epilepsy_modules(human_wgcna)

# 疾病进展轨迹分析
human_trajectory <- perform_trajectory_analysis(human_validated)

# 保存注释结果
saveRDS(human_validated, "data/processed/human_annotated.rds")
saveRDS(mouse_validated, "data/processed/mouse_annotated.rds")
saveRDS(human_deg_results, "results/human_deg_results.rds")
saveRDS(epilepsy_modules, "results/epilepsy_modules.rds")

cat("细胞类型识别与注释完成！\n")

# ===============================================================================
# 第三部分：代谢分析
# ===============================================================================

cat("开始代谢通路分析...\n")

# 3.1 代谢通路活性评分
cat("计算代谢通路活性评分...\n")

# 人类代谢评分
human_metabolic <- calculate_metabolic_scores(human_validated)

# 小鼠代谢评分
mouse_metabolic <- calculate_metabolic_scores(mouse_validated)

# 3.2 代谢异质性分析
cat("进行代谢异质性分析...\n")

# 代谢表型聚类
human_metabolic_clusters <- perform_metabolic_clustering(human_metabolic)

# 代谢状态差异分析
metabolic_differences <- analyze_metabolic_differences(human_metabolic_clusters)

# 3.3 代谢流分析
cat("进行代谢流分析...\n")

# 单细胞代谢流分析
flux_analysis_results <- perform_metabolic_flux_analysis(human_metabolic)

# 保存代谢分析结果
saveRDS(human_metabolic, "data/processed/human_metabolic.rds")
saveRDS(mouse_metabolic, "data/processed/mouse_metabolic.rds")
saveRDS(metabolic_differences, "results/metabolic_differences.rds")
saveRDS(flux_analysis_results, "results/flux_analysis.rds")

cat("代谢分析完成！\n")

# ===============================================================================
# 第四部分：细胞间通讯分析
# ===============================================================================

cat("开始细胞间通讯分析...\n")

# 4.1 配体-受体相互作用分析
cat("进行配体-受体相互作用分析...\n")

# 人类细胞通讯分析
human_cellchat <- analyze_cell_communication(human_metabolic)

# 小鼠细胞通讯分析
mouse_cellchat <- analyze_cell_communication(mouse_metabolic)

# 4.2 代谢相关通讯分析
cat("分析代谢相关细胞通讯...\n")

# 代谢物转移网络
metabolic_communication <- analyze_metabolic_communication(human_cellchat)

# 4.3 时间序列通讯变化分析
cat("分析时间序列通讯变化...\n")

# 小鼠时间序列通讯比较
timeseries_communication <- compare_timeseries_communication(
    mouse_cellchat, 
    timepoints = c("Day7", "Day30")
)

# 保存通讯分析结果
saveRDS(human_cellchat, "results/human_cellchat.rds")
saveRDS(mouse_cellchat, "results/mouse_cellchat.rds")
saveRDS(metabolic_communication, "results/metabolic_communication.rds")
saveRDS(timeseries_communication, "results/timeseries_communication.rds")

cat("细胞间通讯分析完成！\n")

# ===============================================================================
# 第五部分：机器学习预测模型
# ===============================================================================

cat("开始构建机器学习预测模型...\n")

# 5.1 特征工程
cat("进行特征工程...\n")

# 提取多层次特征
prediction_features <- extract_prediction_features(human_metabolic, human_cellchat)

# 特征选择和降维
selected_features <- select_important_features(prediction_features)

# 5.2 模型构建
cat("构建预测模型...\n")

# 准备建模数据
modeling_data <- prepare_modeling_data(selected_features)

# 构建三种机器学习模型
ml_models <- build_prediction_models(
    features = modeling_data$features,
    outcomes = modeling_data$outcomes
)

# 5.3 模型评估
cat("评估模型性能...\n")

# 模型性能评估
model_performance <- evaluate_model_performance(ml_models$models, ml_models$test_data)

# 模型可解释性分析
model_interpretability <- analyze_model_interpretability(ml_models$models, ml_models$test_data)

# 保存建模结果
saveRDS(ml_models, "results/ml_models.rds")
saveRDS(model_performance, "results/model_performance.rds")
saveRDS(model_interpretability, "results/model_interpretability.rds")

cat("机器学习建模完成！\n")

# ===============================================================================
# 第六部分：高质量可视化
# ===============================================================================

cat("开始生成高质量可视化图表...\n")

# 6.1 核心分析图表
cat("制作核心分析图表...\n")

# 细胞类型识别图表
celltype_plots <- create_celltype_plots(human_validated)

# 代谢分析图表
metabolic_plots <- create_metabolic_plots(human_metabolic)

# 细胞通讯图表
communication_plots <- create_communication_plots(human_cellchat)

# 模型性能图表
model_plots <- create_model_performance_plots(model_performance, model_interpretability)

# 6.2 时间序列分析图表
cat("制作时间序列分析图表...\n")

# 轨迹分析图表
trajectory_plots <- create_trajectory_plots(human_trajectory)

# 时间序列通讯图表
timeseries_plots <- create_timeseries_communication_plots(timeseries_communication)

# 6.3 综合分析图表
cat("制作综合分析图表...\n")

# 创建主要结果汇总图
summary_plots <- create_summary_plots(
    human_validated, human_metabolic, human_cellchat, model_performance
)

# 6.4 导出图表
cat("导出高质量图表...\n")

# 导出所有图表
export_publication_figures(
    list(
        celltype = celltype_plots,
        metabolic = metabolic_plots,
        communication = communication_plots,
        models = model_plots,
        trajectory = trajectory_plots,
        timeseries = timeseries_plots,
        summary = summary_plots
    ),
    output_dir = "figures"
)

cat("可视化图表生成完成！\n")

# ===============================================================================
# 第七部分：结果整合与报告生成
# ===============================================================================

cat("开始结果整合与报告生成...\n")

# 7.1 结果汇总
cat("汇总分析结果...\n")

# 创建结果汇总对象
analysis_summary <- create_analysis_summary(
    human_validated = human_validated,
    mouse_validated = mouse_validated,
    deg_results = human_deg_results,
    metabolic_results = metabolic_differences,
    communication_results = metabolic_communication,
    model_results = model_performance,
    trajectory_results = human_trajectory
)

# 7.2 生物学解释
cat("生成生物学解释...\n")

# 生成结果的生物学解释
biological_interpretation <- generate_biological_interpretation(analysis_summary)

# 7.3 生成分析报告
cat("生成分析报告...\n")

# 渲染分析报告
rmarkdown::render(
    "reports/epilepsy_scrna_analysis_report.Rmd",
    params = list(
        analysis_summary = analysis_summary,
        biological_interpretation = biological_interpretation
    ),
    output_file = "epilepsy_scrna_final_report.html"
)

# 7.4 保存最终结果
cat("保存最终结果...\n")

# 保存完整的分析结果
final_results <- list(
    summary = analysis_summary,
    interpretation = biological_interpretation,
    human_data = human_metabolic,
    mouse_data = mouse_metabolic,
    models = ml_models,
    performance = model_performance
)

saveRDS(final_results, "results/final_analysis_results.rds")

# 导出关键结果表格
export_result_tables(final_results, "results/tables")

cat("分析流水线执行完成！\n")

# ===============================================================================
# 第八部分：质量控制与验证
# ===============================================================================

cat("进行最终质量控制检查...\n")

# 8.1 分析质量检查
quality_check_results <- perform_final_quality_checks(final_results)

# 8.2 可重现性验证
reproducibility_check <- validate_reproducibility(human_metabolic)

# 8.3 生成质量报告
generate_quality_report(quality_check_results, reproducibility_check)

cat("质量控制检查完成！\n")

# ===============================================================================
# 分析完成总结
# ===============================================================================

cat("\n" + "="*80 + "\n")
cat("癫痫单细胞转录组学分析流水线执行完成！\n")
cat("="*80 + "\n")

cat("主要输出文件：\n")
cat("- 数据文件: data/processed/\n")
cat("- 分析结果: results/\n") 
cat("- 图表文件: figures/\n")
cat("- 分析报告: reports/epilepsy_scrna_final_report.html\n")

cat("\n关键发现：\n")
print_key_findings(analysis_summary)

cat("\n下一步建议：\n")
print_next_steps(analysis_summary)

cat("\n分析流水线执行时间：", Sys.time() - start_time, "\n")
cat("="*80 + "\n")

# 清理内存
gc()

cat("分析完成！请查看生成的报告和结果文件。\n")
