#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF内容提取和分析工具
用于提取和分析学术论文PDF的内容
"""

import PyPDF2
import pdfplumber
import os
import re
from typing import List, Dict, Any
import json

class PDFAnalyzer:
    def __init__(self):
        self.extracted_content = {}
        
    def extract_text_pypdf2(self, pdf_path: str) -> str:
        """使用PyPDF2提取PDF文本"""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text
        except Exception as e:
            print(f"PyPDF2提取失败: {e}")
            return ""
    
    def extract_text_pdfplumber(self, pdf_path: str) -> str:
        """使用pdfplumber提取PDF文本（通常效果更好）"""
        try:
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            return text
        except Exception as e:
            print(f"pdfplumber提取失败: {e}")
            return ""
    
    def clean_text(self, text: str) -> str:
        """清理提取的文本"""
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff.,;:!?()[\]{}"-]', '', text)
        return text.strip()
    
    def extract_sections(self, text: str) -> Dict[str, str]:
        """提取论文的主要部分"""
        sections = {}
        
        # 常见的论文部分标题模式
        section_patterns = {
            'abstract': r'(?i)(abstract|摘要|概要)',
            'introduction': r'(?i)(introduction|引言|前言|背景)',
            'methods': r'(?i)(methods?|methodology|材料与方法|方法)',
            'results': r'(?i)(results?|结果)',
            'discussion': r'(?i)(discussion|讨论)',
            'conclusion': r'(?i)(conclusion|conclusions?|结论)',
            'references': r'(?i)(references?|参考文献|bibliography)'
        }
        
        # 尝试按标题分割文本
        for section_name, pattern in section_patterns.items():
            matches = list(re.finditer(pattern, text))
            if matches:
                # 找到第一个匹配的位置
                start = matches[0].start()
                # 找到下一个主要部分的开始位置
                next_sections = []
                for other_name, other_pattern in section_patterns.items():
                    if other_name != section_name:
                        other_matches = list(re.finditer(other_pattern, text[start+50:]))
                        if other_matches:
                            next_sections.append(start + 50 + other_matches[0].start())
                
                if next_sections:
                    end = min(next_sections)
                    sections[section_name] = text[start:end].strip()
                else:
                    sections[section_name] = text[start:start+2000].strip()  # 限制长度
        
        return sections
    
    def analyze_pdf(self, pdf_path: str) -> Dict[str, Any]:
        """分析单个PDF文件"""
        print(f"正在分析: {pdf_path}")
        
        # 尝试两种提取方法
        text1 = self.extract_text_pypdf2(pdf_path)
        text2 = self.extract_text_pdfplumber(pdf_path)
        
        # 选择提取效果更好的文本
        text = text2 if len(text2) > len(text1) else text1
        
        if not text:
            return {"error": "无法提取PDF内容"}
        
        # 清理文本
        cleaned_text = self.clean_text(text)
        
        # 提取各个部分
        sections = self.extract_sections(cleaned_text)
        
        # 基本信息提取
        analysis = {
            "filename": os.path.basename(pdf_path),
            "total_length": len(cleaned_text),
            "sections": sections,
            "keywords": self.extract_keywords(cleaned_text),
            "summary": self.generate_summary(cleaned_text, sections)
        }
        
        return analysis
    
    def extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取（基于词频）
        words = re.findall(r'\b\w+\b', text.lower())
        word_freq = {}
        for word in words:
            if len(word) > 3:  # 只考虑长度大于3的词
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # 返回频率最高的前20个词
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_words[:20]]
    
    def generate_summary(self, text: str, sections: Dict[str, str]) -> str:
        """生成摘要"""
        summary_parts = []
        
        # 如果有摘要部分，优先使用
        if 'abstract' in sections:
            summary_parts.append("摘要: " + sections['abstract'][:500])
        
        # 如果有结论部分
        if 'conclusion' in sections:
            summary_parts.append("结论: " + sections['conclusion'][:500])
        
        # 如果没有找到特定部分，使用文本开头
        if not summary_parts:
            summary_parts.append("文档开头: " + text[:1000])
        
        return "\n\n".join(summary_parts)
    
    def analyze_multiple_pdfs(self, pdf_paths: List[str]) -> Dict[str, Any]:
        """分析多个PDF文件"""
        results = {}
        
        for pdf_path in pdf_paths:
            if os.path.exists(pdf_path):
                results[pdf_path] = self.analyze_pdf(pdf_path)
            else:
                results[pdf_path] = {"error": f"文件不存在: {pdf_path}"}
        
        return results
    
    def save_analysis_report(self, analysis_results: Dict[str, Any], output_path: str):
        """保存分析报告"""
        report = []
        report.append("# PDF文档分析报告\n")
        report.append(f"分析时间: {os.popen('date').read().strip()}\n")
        report.append("=" * 50 + "\n")
        
        for pdf_path, analysis in analysis_results.items():
            report.append(f"\n## 文档: {analysis.get('filename', pdf_path)}\n")
            
            if 'error' in analysis:
                report.append(f"错误: {analysis['error']}\n")
                continue
            
            report.append(f"文档长度: {analysis.get('total_length', 0)} 字符\n")
            
            # 关键词
            if 'keywords' in analysis:
                report.append(f"关键词: {', '.join(analysis['keywords'][:10])}\n")
            
            # 摘要
            if 'summary' in analysis:
                report.append(f"摘要:\n{analysis['summary']}\n")
            
            # 各个部分
            if 'sections' in analysis:
                for section_name, section_content in analysis['sections'].items():
                    if section_content:
                        report.append(f"\n### {section_name.upper()}\n")
                        report.append(f"{section_content[:800]}...\n")
            
            report.append("\n" + "=" * 50 + "\n")
        
        # 保存报告
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(''.join(report))
        
        print(f"分析报告已保存到: {output_path}")

def main():
    """主函数"""
    analyzer = PDFAnalyzer()
    
    # 要分析的PDF文件
    pdf_files = [
        "40364_2024_Article_605糖酵解多组学分析 11分.pdf",
        "41467_2024_Article_49133单细胞和空转在AD中的研究.pdf"
    ]
    
    # 分析PDF文件
    results = analyzer.analyze_multiple_pdfs(pdf_files)
    
    # 保存分析报告
    analyzer.save_analysis_report(results, "PDF分析报告.md")
    
    # 也保存为JSON格式以便进一步处理
    with open("PDF分析结果.json", 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print("分析完成！")
    print("生成的文件:")
    print("- PDF分析报告.md (可读性强的报告)")
    print("- PDF分析结果.json (结构化数据)")

if __name__ == "__main__":
    main()
