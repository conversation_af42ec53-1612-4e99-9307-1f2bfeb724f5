# 癫痫相关单细胞转录组学研究完整技术方案

## 项目概述

### 研究目标
联合多组学分析技术阐明糖酵解、乳酸化、脂质代谢等关键代谢通路在难治性癫痫发病过程中脑组织不同细胞类型间的时空动态变化规律及细胞间代谢耦合网络，构建预后预测模型。

### 数据资源整合策略
- **优先级1**: 人类组织标本数据（临床相关性最高）
- **优先级2**: 小鼠时间序列数据整合分析
- **优先级3**: 跨物种比较验证

---

## 第一阶段：数据获取与质量评估

### 1.1 公共数据库数据获取

#### 人类癫痫数据集
```bash
# 1. PRJNA1149456 - 难治性癫痫患者数据（19例）
# SRR30290790-SRR30290808
for i in {30290790..30290808}; do
    prefetch SRR$i
    fasterq-dump SRR$i --split-files
done

# 2. GSE201048 - 人类癫痫标本
# 3. GSE140393 - 人类癫痫相关数据
# 4. GSE197015 - 人类和小鼠癫痫数据
```

#### 小鼠时间序列数据
```bash
# KA模型7天数据（用于与30天数据时间序列分析）
# 从GSE197015获取相关时间点数据
```

### 1.2 数据质量初步评估

#### 质控指标设定
```r
# 细胞质控标准
min_genes_per_cell <- 200
max_genes_per_cell <- 6000
max_mito_percent <- 20
min_cells_per_gene <- 3

# 数据集质控标准
min_cells_per_dataset <- 500
target_cells_per_group <- 2000
```

---

## 第二阶段：数据预处理与标准化

### 2.1 人类数据预处理流程

#### 2.1.1 单个数据集处理
```r
# 使用Seurat v5进行预处理
library(Seurat)
library(SeuratObject)
library(dplyr)

process_human_dataset <- function(data_path, sample_id) {
    # 1. 数据加载
    counts <- Read10X(data_path)
    seurat_obj <- CreateSeuratObject(
        counts = counts,
        project = sample_id,
        min.cells = 3,
        min.features = 200
    )
    
    # 2. 质量控制指标计算
    seurat_obj[["percent.mt"]] <- PercentageFeatureSet(
        seurat_obj, pattern = "^MT-"
    )
    seurat_obj[["percent.ribo"]] <- PercentageFeatureSet(
        seurat_obj, pattern = "^RP[SL]"
    )
    
    # 3. 质控过滤
    seurat_obj <- subset(
        seurat_obj,
        subset = nFeature_RNA > 200 & 
                nFeature_RNA < 6000 & 
                percent.mt < 20
    )
    
    # 4. 标准化和特征选择
    seurat_obj <- NormalizeData(seurat_obj)
    seurat_obj <- FindVariableFeatures(
        seurat_obj, 
        selection.method = "vst",
        nfeatures = 3000
    )
    
    return(seurat_obj)
}
```

#### 2.1.2 批次效应校正
```r
# 使用Harmony进行批次效应校正
library(harmony)

integrate_human_datasets <- function(seurat_list) {
    # 1. 合并数据集
    combined <- merge(seurat_list[[1]], y = seurat_list[-1])
    
    # 2. 标准化和PCA
    combined <- ScaleData(combined)
    combined <- RunPCA(combined, npcs = 50)
    
    # 3. Harmony批次校正
    combined <- RunHarmony(
        combined,
        group.by.vars = "orig.ident",
        reduction = "pca",
        dims.use = 1:50,
        assay.use = "RNA"
    )
    
    # 4. 聚类和UMAP
    combined <- FindNeighbors(combined, reduction = "harmony", dims = 1:30)
    combined <- FindClusters(combined, resolution = 0.5)
    combined <- RunUMAP(combined, reduction = "harmony", dims = 1:30)
    
    return(combined)
}
```

### 2.2 小鼠数据预处理

#### 2.2.1 时间序列数据整合
```r
# 整合7天和30天时间点数据
integrate_mouse_timeseries <- function(day7_data, day30_data) {
    # 添加时间点标签
    day7_data$timepoint <- "Day7"
    day30_data$timepoint <- "Day30"
    
    # 合并数据
    mouse_combined <- merge(day7_data, day30_data)
    
    # 标准化处理
    mouse_combined <- NormalizeData(mouse_combined)
    mouse_combined <- FindVariableFeatures(mouse_combined, nfeatures = 3000)
    mouse_combined <- ScaleData(mouse_combined)
    mouse_combined <- RunPCA(mouse_combined, npcs = 50)
    
    # 时间序列特异性处理
    mouse_combined <- RunHarmony(
        mouse_combined,
        group.by.vars = c("timepoint", "orig.ident"),
        reduction = "pca"
    )
    
    return(mouse_combined)
}
```

---

## 第三阶段：细胞类型识别与注释

### 3.1 自动化细胞类型注释

#### 3.1.1 使用SingleR进行注释
```r
library(SingleR)
library(celldex)

# 人类脑组织参考数据
ref_human <- HumanPrimaryCellAtlasData()
ref_brain <- BlueprintEncodeData()

annotate_cell_types <- function(seurat_obj, species = "human") {
    # 提取表达矩阵
    expr_matrix <- GetAssayData(seurat_obj, slot = "data")
    
    # SingleR注释
    if (species == "human") {
        pred <- SingleR(
            test = expr_matrix,
            ref = ref_human,
            labels = ref_human$label.main
        )
    }
    
    # 添加注释结果
    seurat_obj$celltype_auto <- pred$labels
    seurat_obj$celltype_score <- pred$scores
    
    return(seurat_obj)
}
```

#### 3.1.2 手动验证和精细注释
```r
# 脑组织特异性标志基因
brain_markers <- list(
    "Neurons" = c("RBFOX3", "SYP", "SNAP25"),
    "Astrocytes" = c("GFAP", "AQP4", "S100B"),
    "Microglia" = c("IBA1", "CX3CR1", "P2RY12"),
    "Oligodendrocytes" = c("MBP", "MOG", "PLP1"),
    "OPCs" = c("PDGFRA", "CSPG4", "SOX10"),
    "Endothelial" = c("PECAM1", "VWF", "CLDN5"),
    "Pericytes" = c("PDGFRB", "RGS5", "ACTA2")
)

# 神经元亚型标志基因
neuron_subtypes <- list(
    "Excitatory" = c("SLC17A7", "CAMK2A", "GRIN1"),
    "Inhibitory" = c("GAD1", "GAD2", "SLC32A1"),
    "Parvalbumin" = c("PVALB", "SST", "VIP"),
    "Somatostatin" = c("SST", "NPY", "CORT"),
    "VIP" = c("VIP", "CCK", "CALB2")
)
```

### 3.2 癫痫相关细胞亚群识别

#### 3.2.1 差异表达分析
```r
# 癫痫 vs 对照差异分析
find_epilepsy_markers <- function(seurat_obj) {
    Idents(seurat_obj) <- "condition"
    
    # 每个细胞类型的差异基因
    markers_list <- list()
    for (celltype in unique(seurat_obj$celltype)) {
        subset_obj <- subset(seurat_obj, celltype == celltype)
        markers <- FindMarkers(
            subset_obj,
            ident.1 = "Epilepsy",
            ident.2 = "Control",
            logfc.threshold = 0.25,
            min.pct = 0.1,
            test.use = "wilcox"
        )
        markers_list[[celltype]] <- markers
    }
    
    return(markers_list)
}
```

#### 3.2.2 癫痫特异性亚群发现
```r
# 使用伪时间分析识别疾病进展相关亚群
library(monocle3)

identify_disease_trajectory <- function(seurat_obj) {
    # 转换为monocle3对象
    cds <- as.cell_data_set(seurat_obj)
    
    # 降维和聚类
    cds <- preprocess_cds(cds, num_dim = 50)
    cds <- reduce_dimension(cds)
    cds <- cluster_cells(cds)
    
    # 学习轨迹
    cds <- learn_graph(cds)
    
    # 根据对照组细胞定义起始点
    cds <- order_cells(cds)
    
    return(cds)
}
```

---

## 第四阶段：代谢通路分析

### 4.1 代谢通路富集分析

#### 4.1.1 关键代谢通路定义
```r
# 重点关注的代谢通路
key_metabolic_pathways <- list(
    "Glycolysis" = c("HK1", "HK2", "GPI", "PFKM", "ALDOA", "TPI1", 
                     "GAPDH", "PGK1", "PGAM1", "ENO1", "PKM"),
    "Lactate_metabolism" = c("LDHA", "LDHB", "SLC16A1", "SLC16A3", 
                            "SLC16A7", "MCT1", "MCT4"),
    "Lipid_metabolism" = c("FASN", "ACACA", "SCD", "FADS1", "FADS2",
                          "ELOVL6", "CPT1A", "ACOX1"),
    "TCA_cycle" = c("CS", "ACO2", "IDH1", "IDH2", "OGDH", "SUCLA2",
                    "SDHA", "FH", "MDH2"),
    "Oxidative_phosphorylation" = c("NDUFA1", "SDHA", "UQCRC1", 
                                   "COX4I1", "ATP5A1")
)
```

#### 4.1.2 单细胞代谢评分
```r
library(scMetabolism)

calculate_metabolic_scores <- function(seurat_obj) {
    # 计算代谢通路活性评分
    seurat_obj <- sc.metabolism.Seurat(
        obj = seurat_obj,
        method = "VISION",
        imputation = F,
        ncores = 8,
        metabolism.type = "KEGG"
    )
    
    # 自定义代谢通路评分
    for (pathway_name in names(key_metabolic_pathways)) {
        genes <- key_metabolic_pathways[[pathway_name]]
        seurat_obj <- AddModuleScore(
            seurat_obj,
            features = list(genes),
            name = paste0(pathway_name, "_score")
        )
    }
    
    return(seurat_obj)
}
```

### 4.2 代谢异质性分析

#### 4.2.1 细胞类型特异性代谢特征
```r
# 代谢表型聚类
perform_metabolic_clustering <- function(seurat_obj) {
    # 提取代谢评分
    metabolic_scores <- <EMAIL>[, grep("_score", colnames(<EMAIL>))]
    
    # 代谢聚类
    metabolic_clusters <- kmeans(metabolic_scores, centers = 5)
    seurat_obj$metabolic_cluster <- metabolic_clusters$cluster
    
    # 代谢状态注释
    seurat_obj$metabolic_state <- case_when(
        seurat_obj$Glycolysis_score1 > 0.5 ~ "High_Glycolysis",
        seurat_obj$Oxidative_phosphorylation_score1 > 0.5 ~ "High_OXPHOS",
        TRUE ~ "Mixed_Metabolism"
    )
    
    return(seurat_obj)
}
```

---

## 第五阶段：细胞间通讯网络分析

### 5.1 配体-受体相互作用分析

#### 5.1.1 使用CellChat进行通讯分析
```r
library(CellChat)

analyze_cell_communication <- function(seurat_obj) {
    # 创建CellChat对象
    cellchat <- createCellChat(
        object = seurat_obj,
        group.by = "celltype",
        assay = "RNA"
    )
    
    # 设置配体-受体数据库
    CellChatDB <- CellChatDB.human
    cellchat@DB <- CellChatDB
    
    # 预处理
    cellchat <- subsetData(cellchat)
    cellchat <- identifyOverExpressedGenes(cellchat)
    cellchat <- identifyOverExpressedInteractions(cellchat)
    
    # 推断细胞通讯
    cellchat <- computeCommunProb(cellchat, raw.use = TRUE)
    cellchat <- filterCommunication(cellchat, min.cells = 10)
    cellchat <- computeCommunProbPathway(cellchat)
    cellchat <- aggregateNet(cellchat)
    
    return(cellchat)
}
```

#### 5.1.2 代谢物转移网络构建
```r
# 代谢物相关的配体-受体对
metabolic_LR_pairs <- list(
    "Lactate_transport" = c("SLC16A1", "SLC16A3", "SLC16A7"),
    "Glucose_transport" = c("SLC2A1", "SLC2A3", "SLC2A4"),
    "Fatty_acid_transport" = c("CD36", "FABP3", "FABP7"),
    "Neurotransmitter" = c("SLC1A2", "SLC1A3", "GRIA1", "GRIA2")
)

analyze_metabolic_communication <- function(cellchat_obj) {
    # 筛选代谢相关通讯
    metabolic_pathways <- c("GLUCOSE", "LACTATE", "LIPID", "NEUROTRANSMITTER")
    
    metabolic_comm <- subsetCommunication(
        cellchat_obj,
        slot.name = "netP",
        sources.use = NULL,
        targets.use = NULL,
        signaling = metabolic_pathways
    )
    
    return(metabolic_comm)
}
```

### 5.2 时间序列通讯变化分析

#### 5.2.1 小鼠时间序列通讯比较
```r
compare_timeseries_communication <- function(day7_cellchat, day30_cellchat) {
    # 合并CellChat对象
    cellchat_list <- list("Day7" = day7_cellchat, "Day30" = day30_cellchat)
    cellchat_merged <- mergeCellChat(cellchat_list, add.names = names(cellchat_list))
    
    # 比较通讯强度
    gg1 <- compareInteractions(cellchat_merged, show.legend = F, group = c(1,2))
    gg2 <- compareInteractions(cellchat_merged, show.legend = F, group = c(1,2), measure = "weight")
    
    # 通讯网络比较
    par(mfrow = c(1,2), xpd=TRUE)
    netVisual_diffInteraction(cellchat_merged, weight.scale = T)
    netVisual_diffInteraction(cellchat_merged, weight.scale = T, measure = "weight")
    
    return(cellchat_merged)
}
```

---

## 第六阶段：空间分析整合

### 6.1 空间共定位分析

#### 6.1.1 模拟空间信息重建
```r
library(Giotto)

# 基于单细胞数据推断空间关系
infer_spatial_relationships <- function(seurat_obj) {
    # 使用SpaOTsc进行空间重建
    library(SpaOTsc)
    
    # 计算细胞间距离
    cell_distances <- dist(Embeddings(seurat_obj, "umap"))
    
    # 构建空间邻接矩阵
    spatial_network <- as.matrix(cell_distances < quantile(cell_distances, 0.1))
    
    # 空间自相关分析
    spatial_autocorr <- calculate_spatial_autocorrelation(
        seurat_obj, 
        spatial_network,
        features = key_metabolic_pathways$Glycolysis
    )
    
    return(spatial_autocorr)
}
```

### 6.2 代谢梯度分析

#### 6.2.1 空间代谢梯度计算
```r
calculate_metabolic_gradients <- function(seurat_obj, spatial_coords) {
    # 代谢评分的空间分布
    metabolic_data <- <EMAIL>[, grep("_score", colnames(<EMAIL>))]
    
    # 计算空间梯度
    gradients <- list()
    for (pathway in colnames(metabolic_data)) {
        # 使用局部回归计算梯度
        gradient <- calculate_spatial_gradient(
            coords = spatial_coords,
            values = metabolic_data[, pathway]
        )
        gradients[[pathway]] <- gradient
    }
    
    return(gradients)
}
```

---

## 第七阶段：机器学习预后预测模型

### 7.1 特征工程

#### 7.1.1 多层次特征提取
```r
extract_prediction_features <- function(seurat_obj) {
    features_list <- list()
    
    # 1. 细胞类型比例特征
    celltype_props <- table(seurat_obj$celltype, seurat_obj$sample_id)
    celltype_props <- prop.table(celltype_props, margin = 2)
    features_list$celltype_proportions <- t(celltype_props)
    
    # 2. 代谢评分特征
    metabolic_scores <- aggregate(
        <EMAIL>[, grep("_score", colnames(<EMAIL>))],
        by = list(seurat_obj$sample_id),
        FUN = mean
    )
    features_list$metabolic_scores <- metabolic_scores
    
    # 3. 差异基因表达特征
    deg_features <- extract_deg_features(seurat_obj)
    features_list$deg_expression <- deg_features
    
    # 4. 细胞通讯强度特征
    comm_features <- extract_communication_features(seurat_obj)
    features_list$communication_strength <- comm_features
    
    return(features_list)
}
```

### 7.2 模型构建与比较

#### 7.2.1 三种机器学习算法实现
```r
library(randomForest)
library(xgboost)
library(glmnet)
library(caret)

build_prediction_models <- function(features, outcomes) {
    # 数据分割
    set.seed(123)
    train_idx <- createDataPartition(outcomes, p = 0.8, list = FALSE)
    
    X_train <- features[train_idx, ]
    X_test <- features[-train_idx, ]
    y_train <- outcomes[train_idx]
    y_test <- outcomes[-train_idx]
    
    models <- list()
    
    # 1. Random Forest
    rf_model <- randomForest(
        x = X_train,
        y = as.factor(y_train),
        ntree = 500,
        mtry = sqrt(ncol(X_train)),
        importance = TRUE
    )
    models$RandomForest <- rf_model
    
    # 2. XGBoost
    xgb_train <- xgb.DMatrix(data = as.matrix(X_train), label = y_train)
    xgb_test <- xgb.DMatrix(data = as.matrix(X_test), label = y_test)
    
    xgb_model <- xgboost(
        data = xgb_train,
        nrounds = 100,
        objective = "binary:logistic",
        eval_metric = "auc",
        early_stopping_rounds = 10,
        verbose = 0
    )
    models$XGBoost <- xgb_model
    
    # 3. Elastic Net
    cv_fit <- cv.glmnet(
        x = as.matrix(X_train),
        y = y_train,
        family = "binomial",
        alpha = 0.5,
        nfolds = 5
    )
    
    elastic_model <- glmnet(
        x = as.matrix(X_train),
        y = y_train,
        family = "binomial",
        alpha = 0.5,
        lambda = cv_fit$lambda.min
    )
    models$ElasticNet <- elastic_model
    
    return(list(models = models, test_data = list(X_test = X_test, y_test = y_test)))
}
```

#### 7.2.2 模型性能评估
```r
evaluate_model_performance <- function(models, test_data) {
    X_test <- test_data$X_test
    y_test <- test_data$y_test
    
    performance_metrics <- data.frame()
    
    for (model_name in names(models)) {
        model <- models[[model_name]]
        
        # 预测
        if (model_name == "RandomForest") {
            pred_prob <- predict(model, X_test, type = "prob")[, 2]
            pred_class <- predict(model, X_test)
        } else if (model_name == "XGBoost") {
            pred_prob <- predict(model, as.matrix(X_test))
            pred_class <- ifelse(pred_prob > 0.5, 1, 0)
        } else if (model_name == "ElasticNet") {
            pred_prob <- predict(model, as.matrix(X_test), type = "response")[, 1]
            pred_class <- ifelse(pred_prob > 0.5, 1, 0)
        }
        
        # 计算性能指标
        auc <- pROC::auc(y_test, pred_prob)
        accuracy <- mean(pred_class == y_test)
        sensitivity <- sum(pred_class == 1 & y_test == 1) / sum(y_test == 1)
        specificity <- sum(pred_class == 0 & y_test == 0) / sum(y_test == 0)
        
        performance_metrics <- rbind(performance_metrics, data.frame(
            Model = model_name,
            AUC = auc,
            Accuracy = accuracy,
            Sensitivity = sensitivity,
            Specificity = specificity
        ))
    }
    
    return(performance_metrics)
}
```

---

## 第八阶段：高质量可视化

### 8.1 发表级图表制作标准

#### 8.1.1 色彩方案设置
```r
# 色盲友好的调色板
library(RColorBrewer)
library(viridis)

# 定义标准色彩方案
color_schemes <- list(
    celltype_colors = c(
        "Neurons" = "#E31A1C",
        "Astrocytes" = "#1F78B4", 
        "Microglia" = "#33A02C",
        "Oligodendrocytes" = "#FF7F00",
        "OPCs" = "#6A3D9A",
        "Endothelial" = "#FB9A99",
        "Pericytes" = "#A6CEE3"
    ),
    condition_colors = c(
        "Control" = "#2166AC",
        "Epilepsy" = "#D73027"
    ),
    timepoint_colors = c(
        "Day7" = "#4575B4",
        "Day30" = "#D73027"
    ),
    metabolic_colors = viridis(100)
)
```

#### 8.1.2 图表主题设置
```r
# 统一的ggplot主题
publication_theme <- theme_classic() +
    theme(
        text = element_text(size = 12, family = "Arial"),
        axis.title = element_text(size = 14, face = "bold"),
        axis.text = element_text(size = 12),
        legend.title = element_text(size = 12, face = "bold"),
        legend.text = element_text(size = 10),
        plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
        panel.grid.major = element_blank(),
        panel.grid.minor = element_blank(),
        axis.line = element_line(colour = "black", size = 0.5)
    )

# 设置为默认主题
theme_set(publication_theme)
```

### 8.2 核心分析图表

#### 8.2.1 细胞类型识别图表
```r
create_celltype_plots <- function(seurat_obj) {
    plots <- list()
    
    # 1. UMAP聚类图
    plots$umap_clusters <- DimPlot(
        seurat_obj,
        reduction = "umap",
        group.by = "celltype",
        cols = color_schemes$celltype_colors,
        pt.size = 0.5
    ) + 
    ggtitle("Cell Type Identification") +
    guides(color = guide_legend(override.aes = list(size = 3)))
    
    # 2. 标志基因热图
    plots$marker_heatmap <- DoHeatmap(
        seurat_obj,
        features = unlist(brain_markers),
        group.by = "celltype",
        size = 3,
        angle = 90
    ) +
    scale_fill_viridis_c() +
    theme(axis.text.y = element_text(size = 8))
    
    # 3. 细胞类型比例图
    celltype_props <- <EMAIL> %>%
        group_by(sample_id, condition, celltype) %>%
        summarise(count = n()) %>%
        group_by(sample_id, condition) %>%
        mutate(proportion = count / sum(count))
    
    plots$proportion_barplot <- ggplot(celltype_props, aes(x = sample_id, y = proportion, fill = celltype)) +
        geom_bar(stat = "identity") +
        facet_wrap(~condition, scales = "free_x") +
        scale_fill_manual(values = color_schemes$celltype_colors) +
        labs(x = "Sample", y = "Cell Type Proportion", fill = "Cell Type") +
        theme(axis.text.x = element_text(angle = 45, hjust = 1))
    
    return(plots)
}
```

#### 8.2.2 代谢分析图表
```r
create_metabolic_plots <- function(seurat_obj) {
    plots <- list()
    
    # 1. 代谢通路活性热图
    metabolic_scores <- <EMAIL>[, grep("_score", colnames(<EMAIL>))]
    metabolic_avg <- aggregate(metabolic_scores, 
                              by = list(seurat_obj$celltype, seurat_obj$condition), 
                              FUN = mean)
    
    plots$metabolic_heatmap <- pheatmap::pheatmap(
        t(metabolic_avg[, -c(1,2)]),
        annotation_col = data.frame(
            CellType = metabolic_avg$Group.1,
            Condition = metabolic_avg$Group.2
        ),
        color = viridis(100),
        cluster_rows = TRUE,
        cluster_cols = TRUE,
        fontsize = 10
    )
    
    # 2. 糖酵解评分小提琴图
    plots$glycolysis_violin <- VlnPlot(
        seurat_obj,
        features = "Glycolysis_score1",
        group.by = "celltype",
        split.by = "condition",
        cols = color_schemes$condition_colors,
        pt.size = 0
    ) +
    labs(title = "Glycolysis Activity by Cell Type", y = "Glycolysis Score")
    
    # 3. 代谢状态UMAP
    plots$metabolic_umap <- FeaturePlot(
        seurat_obj,
        features = c("Glycolysis_score1", "Oxidative_phosphorylation_score1"),
        reduction = "umap",
        cols = viridis(100),
        ncol = 2
    )
    
    return(plots)
}
```

#### 8.2.3 细胞通讯网络图
```r
create_communication_plots <- function(cellchat_obj) {
    plots <- list()
    
    # 1. 通讯网络圈图
    plots$circle_plot <- netVisual_circle(
        cellchat_obj@net$count,
        vertex.weight = groupSize,
        weight.scale = T,
        label.edge = F,
        title.name = "Number of interactions"
    )
    
    # 2. 通讯强度热图
    plots$interaction_heatmap <- netVisual_heatmap(
        cellchat_obj,
        signaling = "GLUCOSE",
        color.heatmap = "Reds"
    )
    
    # 3. 信号通路层次图
    plots$hierarchy_plot <- netVisual_hierarchy1(
        cellchat_obj,
        signaling = "LACTATE",
        vertex.receiver = seq(1,4)
    )
    
    return(plots)
}
```

### 8.3 时间序列分析图表

#### 8.3.1 轨迹分析可视化
```r
create_trajectory_plots <- function(monocle_obj) {
    plots <- list()
    
    # 1. 伪时间轨迹图
    plots$trajectory <- plot_cells(
        monocle_obj,
        color_cells_by = "pseudotime",
        label_cell_groups = FALSE,
        label_leaves = FALSE,
        label_branch_points = FALSE,
        graph_label_size = 1.5
    ) +
    scale_color_viridis_c() +
    ggtitle("Pseudotime Trajectory")
    
    # 2. 基因表达沿轨迹变化
    plots$gene_dynamics <- plot_genes_in_pseudotime(
        monocle_obj[key_metabolic_pathways$Glycolysis, ],
        color_cells_by = "condition",
        min_expr = 0.5
    )
    
    return(plots)
}
```

---

## 第九阶段：统计分析与结果解释

### 9.1 统计方法设定

#### 9.1.1 差异分析统计
```r
# 统计检验参数设定
statistical_params <- list(
    # 差异表达分析
    deg_analysis = list(
        test = "wilcox",
        logfc_threshold = 0.25,
        min_pct = 0.1,
        p_adjust_method = "BH",
        significance_threshold = 0.05
    ),
    
    # 通路富集分析
    pathway_analysis = list(
        p_cutoff = 0.05,
        q_cutoff = 0.2,
        min_gene_set_size = 10,
        max_gene_set_size = 500
    ),
    
    # 细胞比例比较
    proportion_test = list(
        test = "fisher.test",
        p_adjust_method = "BH"
    )
)
```

#### 9.1.2 多重比较校正
```r
perform_statistical_analysis <- function(seurat_obj) {
    results <- list()
    
    # 1. 细胞类型比例统计检验
    celltype_stats <- compare_celltype_proportions(seurat_obj)
    results$celltype_proportions <- celltype_stats
    
    # 2. 代谢评分统计比较
    metabolic_stats <- compare_metabolic_scores(seurat_obj)
    results$metabolic_comparison <- metabolic_stats
    
    # 3. 差异基因富集分析
    enrichment_results <- perform_pathway_enrichment(seurat_obj)
    results$pathway_enrichment <- enrichment_results
    
    return(results)
}
```

### 9.2 生物学解释框架

#### 9.2.1 结果解释模板
```r
generate_biological_interpretation <- function(analysis_results) {
    interpretation <- list()
    
    # 1. 细胞类型变化的生物学意义
    interpretation$celltype_changes <- "
    观察到的细胞类型比例变化反映了癫痫病理过程中的细胞死亡、
    增殖和迁移模式。特别是神经元减少和胶质细胞增加提示了
    神经炎症和胶质瘢痕形成的病理过程。
    "
    
    # 2. 代谢重编程的机制解释
    interpretation$metabolic_reprogramming <- "
    糖酵解通路的上调反映了癫痫发作时的高能量需求和缺氧环境。
    乳酸代谢的改变可能导致细胞外pH变化，影响神经元兴奋性。
    脂质代谢异常可能与膜稳定性和髓鞘完整性相关。
    "
    
    # 3. 细胞通讯网络的病理意义
    interpretation$communication_networks <- "
    细胞间通讯网络的重构反映了癫痫病理状态下的
    神经-胶质相互作用异常。代谢物转移网络的改变
    可能是疾病进展的关键驱动因素。
    "
    
    return(interpretation)
}
```

---

## 第十阶段：质量控制与验证

### 10.1 分析质量控制检查点

#### 10.1.1 数据质量检查
```r
quality_control_checklist <- function(seurat_obj) {
    qc_results <- list()
    
    # 1. 细胞数量检查
    qc_results$cell_numbers <- table(seurat_obj$sample_id, seurat_obj$condition)
    
    # 2. 基因检测率检查
    qc_results$gene_detection <- summary(seurat_obj$nFeature_RNA)
    
    # 3. 线粒体基因比例检查
    qc_results$mito_percent <- summary(seurat_obj$percent.mt)
    
    # 4. 批次效应检查
    qc_results$batch_effect <- assess_batch_effect(seurat_obj)
    
    # 5. 聚类稳定性检查
    qc_results$cluster_stability <- assess_cluster_stability(seurat_obj)
    
    return(qc_results)
}
```

#### 10.1.2 结果可重现性验证
```r
validate_reproducibility <- function(seurat_obj) {
    # 1. 随机种子设置记录
    set.seed(123)
    
    # 2. 关键分析步骤重复
    validation_results <- list()
    
    # 重复聚类分析
    for (i in 1:5) {
        set.seed(123 + i)
        temp_obj <- FindClusters(seurat_obj, resolution = 0.5)
        validation_results[[paste0("clustering_", i)]] <- temp_obj$seurat_clusters
    }
    
    # 计算聚类一致性
    cluster_consistency <- calculate_cluster_consistency(validation_results)
    
    return(cluster_consistency)
}
```

---

## 项目时间线与里程碑

### 阶段1-2周：数据获取与预处理
- [ ] 公共数据下载和质量评估
- [ ] 数据标准化和批次校正
- [ ] 初步质控报告

### 阶段2-3周：细胞类型识别与注释
- [ ] 自动化细胞类型注释
- [ ] 手动验证和精细注释
- [ ] 癫痫特异性亚群识别

### 阶段3-4周：代谢分析与细胞通讯
- [ ] 代谢通路富集分析
- [ ] 细胞间通讯网络构建
- [ ] 时间序列分析

### 阶段4-5周：机器学习与预测模型
- [ ] 特征工程和模型训练
- [ ] 模型验证和性能评估
- [ ] 结果解释和生物学验证

### 阶段5-6周：可视化与报告
- [ ] 高质量图表制作
- [ ] 分析报告撰写
- [ ] 代码整理和文档化

---

## 预期交付成果

### 1. 数据文件
- 清洗后的原始数据矩阵
- 中间分析结果文件
- 最终整合数据集

### 2. 分析代码
- 完整的R/Python分析脚本
- 详细的代码注释
- 环境配置文件

### 3. 结果文件
- 差异基因列表
- 代谢通路富集结果
- 细胞通讯网络数据
- 预测模型性能报告

### 4. 可视化文件
- 高分辨率图表（PDF/SVG格式）
- 交互式可视化文件
- 图表源文件和代码

### 5. 分析报告
- 详细的方法学描述
- 结果解释和生物学意义
- 发表级别的图表说明

---

## 技术支持与质量保证

### 软件环境要求
```r
# R环境配置
R_version: "4.3.0"
required_packages:
  - Seurat: "5.0.0"
  - harmony: "1.0"
  - SingleR: "2.0.0"
  - CellChat: "1.6.0"
  - monocle3: "1.3.0"
  - scMetabolism: "0.2.1"
```

### 计算资源需求
- 内存：至少32GB RAM
- 存储：至少500GB可用空间
- CPU：多核处理器（推荐16核以上）
- GPU：可选，用于深度学习模型

### 质量保证措施
1. 每个分析步骤的质控检查
2. 关键结果的独立验证
3. 代码审查和测试
4. 结果可重现性验证
5. 定期进度汇报和反馈

---

*本方案将根据实际数据特点和分析进展进行动态调整和优化。*
